require("@nomicfoundation/hardhat-toolbox")
require("@openzeppelin/hardhat-upgrades")
require("dotenv").config()
require("hardhat-gas-reporter")
require("solidity-coverage")
require("hardhat-deploy")
const Sepolia_Rpc_Url = process.env.Sepolia_Rpc_Url
const Private_Key = process.env.Private_Key
const Etherscan_Api_Key = process.env.Etherscan_Api_Key
const Bsc_Rpc_Url = process.env.Bsc_Rpc_Url
const Bsc_Api_Key = process.env.Bsc_Api_Key
const Mainnet_Rpc_Url = process.env.Mainnet_Rpc_Url || "https://mainnet.infura.io/v3/YOUR_PROJECT_ID"

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
    defaultNetwork: "hardhat",
    networks: {
        hardhat: {
            chainId: 31337,
            blockConfirmations: 1,
        },
        Sepolia: {
            chainId: ********,
            blockConfirmations: 6,
            url: Sepolia_Rpc_Url,
            accounts: [Private_Key],
        },
        bscTestnet: {
            chainId: 97,
            url: Bsc_Rpc_Url,
            accounts: [Private_Key],
        },
        mainnet: {
            chainId: 1,
            blockConfirmations: 6,
            url: Mainnet_Rpc_Url,
            accounts: [Private_Key],
            gasPrice: ***********, // 20 gwei
        },
    },
    gasReporter: {
        enabled: false,
        outputFile: "gas-report.txt",
        noColors: true,
        currency: "USD",
    },

    solidity: "0.8.28",
    namedAccounts: {
        deployer: {
            default: 0,
        },
        player: {
            default: 1,
        },
    },
    mocha: {
        timeout: 200000, //200 secs max
    },
    etherscan: {
        apiKey: {
            sepolia: Etherscan_Api_Key,
            bscTestnet: Bsc_Api_Key,
            mainnet: Etherscan_Api_Key,
        },
    },
}
